# Create the public directory structure for Vercel
import os

# Create directories
os.makedirs('public', exist_ok=True)
os.makedirs('api', exist_ok=True)

# Read the existing HTML frontend and modify it for Vercel
with open('chat_frontend.html', 'r') as f:
    frontend_content = f.read()

# Modify the frontend to work with Vercel's serverless functions instead of WebSocket
vercel_frontend = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mini Network Chat - Vercel Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .chat-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .connection-status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background: #28a745;
        }

        .status-disconnected {
            background: #dc3545;
        }

        .login-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            padding: 40px;
        }

        .login-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            min-width: 300px;
        }

        .login-form h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .login-form input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .login-form input:focus {
            border-color: #667eea;
        }

        .login-form button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-form button:hover {
            transform: translateY(-2px);
        }

        .login-form button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .chat-main {
            display: none;
            flex: 1;
            flex-direction: column;
        }

        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .message.own {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.other {
            background: white;
            border: 1px solid #dee2e6;
            margin-right: auto;
        }

        .message.system {
            background: #e9ecef;
            text-align: center;
            margin: 10px auto;
            font-style: italic;
            color: #6c757d;
            max-width: 50%;
        }

        .message-sender {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            opacity: 0.8;
        }

        .message-content {
            font-size: 16px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .info-banner {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin: 20px;
            text-align: center;
            font-size: 14px;
        }

        .info-banner strong {
            display: block;
            margin-bottom: 8px;
            font-size: 16px;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .message {
                max-width: 85%;
            }
            
            .login-form {
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="chat-title">💬 Mini Network Chat</div>
            <div class="chat-subtitle">Vercel Serverless Edition</div>
        </div>
        
        <div class="info-banner">
            <strong>🚀 Deployed on Vercel!</strong>
            This is a serverless version using HTTP polling instead of WebSocket. 
            Messages refresh every 2 seconds for real-time feel.
        </div>
        
        <div class="connection-status">
            <div style="display: flex; align-items: center;">
                <div class="status-indicator status-connected" id="statusIndicator"></div>
                <span id="statusText">Connected</span>
            </div>
            <div>
                <span id="userCount">0 users online</span>
            </div>
        </div>

        <!-- Login Screen -->
        <div class="login-screen" id="loginScreen">
            <div class="login-form">
                <h2>Join the Chat</h2>
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="30" required>
                <button onclick="joinChat()" id="connectButton">Join Chat</button>
            </div>
        </div>

        <!-- Main Chat Interface -->
        <div class="chat-main" id="chatMain">
            <div class="messages-container" id="messagesContainer">
                <!-- Messages will be added here dynamically -->
            </div>
            
            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Type your message..." maxlength="2000">
                <button onclick="sendMessage()" id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        let username = '';
        let connected = false;
        let lastMessageId = 0;
        let pollInterval;

        // DOM elements
        const loginScreen = document.getElementById('loginScreen');
        const chatMain = document.getElementById('chatMain');
        const usernameInput = document.getElementById('usernameInput');
        const messageInput = document.getElementById('messageInput');
        const messagesContainer = document.getElementById('messagesContainer');
        const statusText = document.getElementById('statusText');
        const userCount = document.getElementById('userCount');
        const connectButton = document.getElementById('connectButton');
        const sendButton = document.getElementById('sendButton');
        const errorMessage = document.getElementById('errorMessage');

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        async function joinChat() {
            const usernameValue = usernameInput.value.trim();

            if (!usernameValue) {
                showError('Please enter a username');
                return;
            }

            if (usernameValue.length < 2 || usernameValue.length > 30) {
                showError('Username must be between 2 and 30 characters');
                return;
            }

            username = usernameValue;
            connectButton.disabled = true;
            connectButton.textContent = 'Joining...';

            try {
                const response = await fetch('/api/join', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username: username })
                });

                const data = await response.json();

                if (data.success) {
                    connected = true;
                    loginScreen.style.display = 'none';
                    chatMain.style.display = 'flex';
                    messageInput.focus();
                    
                    // Start polling for messages
                    startPolling();
                    
                    addSystemMessage(`Welcome ${username}! You joined the chat.`);
                } else {
                    showError(data.error || 'Failed to join chat');
                    connectButton.disabled = false;
                    connectButton.textContent = 'Join Chat';
                }
            } catch (error) {
                showError('Connection failed. Please try again.');
                connectButton.disabled = false;
                connectButton.textContent = 'Join Chat';
            }
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            
            if (!message || !connected) {
                return;
            }

            sendButton.disabled = true;
            const originalText = sendButton.textContent;
            sendButton.textContent = 'Sending...';

            try {
                const response = await fetch('/api/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username: username,
                        message: message 
                    })
                });

                const data = await response.json();

                if (data.success) {
                    messageInput.value = '';
                    // Message will appear via polling
                } else {
                    showError('Failed to send message');
                }
            } catch (error) {
                showError('Failed to send message');
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = originalText;
            }
        }

        async function pollMessages() {
            try {
                const response = await fetch(`/api/messages?since=${lastMessageId}`);
                const data = await response.json();

                if (data.success && data.messages) {
                    data.messages.forEach(msg => {
                        if (msg.type === 'message') {
                            addMessage(msg.username, msg.message, msg.username === username);
                        } else if (msg.type === 'system') {
                            addSystemMessage(msg.message);
                        }
                        if (msg.id > lastMessageId) {
                            lastMessageId = msg.id;
                        }
                    });

                    if (data.userCount !== undefined) {
                        updateUserCount(data.userCount);
                    }
                }
            } catch (error) {
                console.log('Polling error:', error);
            }
        }

        function startPolling() {
            pollMessages(); // Initial load
            pollInterval = setInterval(pollMessages, 2000); // Poll every 2 seconds
        }

        function addMessage(sender, message, isOwn) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isOwn ? 'own' : 'other'}`;
            
            const time = new Date().toLocaleTimeString(undefined, {
                hour: '2-digit',
                minute: '2-digit'
            });

            messageDiv.innerHTML = `
                ${!isOwn ? `<div class="message-sender">${escapeHtml(sender)}</div>` : ''}
                <div class="message-content">${escapeHtml(message)}</div>
                <div class="message-time">${time}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function addSystemMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `<div class="message-content">${escapeHtml(message)}</div>`;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function updateUserCount(count) {
            userCount.textContent = `${count} user${count !== 1 ? 's' : ''} online`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Event listeners
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                joinChat();
            }
        });

        // Auto-focus username input
        usernameInput.focus();

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        });

        // Initialize with some demo messages
        setTimeout(() => {
            if (!connected) {
                // Show demo messages if not connected yet
                addSystemMessage("This is a demo of the Vercel-deployed chat!");
                addMessage("Alice", "Hi everyone! This chat is now serverless!", false);
                addMessage("Bob", "Amazing! It works without WebSockets too.", false);
            }
        }, 1000);
    </script>
</body>
</html>'''

# Save to public directory
with open('public/index.html', 'w') as f:
    f.write(vercel_frontend)

print("Vercel-compatible frontend created: public/index.html")