<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mini Network Chat - Real-Time</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .chat-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .connection-status {
            padding: 10px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .status-disconnected {
            background: #dc3545 !important;
            animation: none;
        }

        .login-screen {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            padding: 40px;
        }

        .login-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            min-width: 300px;
        }

        .login-form h2 {
            margin-bottom: 20px;
            color: #333;
        }

        .login-form input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .login-form input:focus {
            border-color: #667eea;
        }

        .login-form button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-form button:hover {
            transform: translateY(-2px);
        }

        .login-form button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .chat-main {
            display: none;
            flex: 1;
            flex-direction: column;
            min-height: 0; /* Important for flexbox scrolling */
            position: relative; /* For scroll button positioning */
        }

        .messages-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            overflow-x: hidden;
            background: #f8f9fa;
            min-height: 0; /* Important for flexbox scrolling */
            max-height: calc(90vh - 200px); /* Ensure it doesn't exceed container */
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }

        /* Custom scrollbar styling */
        .messages-container::-webkit-scrollbar {
            width: 8px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Scroll to bottom button */
        .scroll-to-bottom {
            position: absolute;
            bottom: 80px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-bottom.visible {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-bottom:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
            animation: fadeIn 0.3s ease-in;
        }

        .message.own {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.other {
            background: white;
            border: 1px solid #dee2e6;
            margin-right: auto;
        }

        .message.system {
            background: #e9ecef;
            text-align: center;
            margin: 10px auto;
            font-style: italic;
            color: #6c757d;
            max-width: 50%;
        }

        .message-sender {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 14px;
            opacity: 0.8;
        }

        .message-content {
            font-size: 16px;
            line-height: 1.4;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #dee2e6;
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: scale(1.05);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }

        .real-time-badge {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }

            .messages-container {
                max-height: calc(100vh - 200px); /* Adjust for mobile */
                padding: 15px;
            }

            .message {
                max-width: 85%;
            }

            .login-form {
                min-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="chat-title">💬 Mini Network Chat</div>
            <div class="chat-subtitle">
                <span class="real-time-badge">REAL-TIME</span>
                No bots • Real people only
            </div>
        </div>

        <div class="connection-status">
            <div style="display: flex; align-items: center;">
                <div class="status-indicator" id="statusIndicator"></div>
                <span id="statusText">Connected</span>
            </div>
            <div>
                <span id="userCount">0 users online</span>
            </div>
        </div>

        <!-- Login Screen -->
        <div class="login-screen" id="loginScreen">
            <div class="login-form">
                <h2>Join Real-Time Chat</h2>
                <p style="margin-bottom: 20px; color: #666; font-size: 14px;">
                    Connect with real people • No bots or fake messages
                </p>
                <div id="errorMessage" class="error-message" style="display: none;"></div>
                <input type="text" id="usernameInput" placeholder="Enter your username" maxlength="30" required>
                <button onclick="joinChat()" id="connectButton">Join Chat</button>
            </div>
        </div>

        <!-- Main Chat Interface -->
        <div class="chat-main" id="chatMain">
            <div class="messages-container" id="messagesContainer">
                <!-- Real messages will appear here -->
            </div>

            <!-- Scroll to bottom button -->
            <button class="scroll-to-bottom" id="scrollToBottomBtn" onclick="scrollToBottom(true)" title="Scroll to bottom">
                ↓
            </button>

            <div class="input-container">
                <input type="text" id="messageInput" class="message-input" placeholder="Type your message..." maxlength="1000">
                <button onclick="sendMessage()" id="sendButton" class="send-button">Send</button>
            </div>
        </div>
    </div>

    <script>
        let username = '';
        let connected = false;
        let eventSource = null;
        let lastMessageId = 0;

        // DOM elements
        const loginScreen = document.getElementById('loginScreen');
        const chatMain = document.getElementById('chatMain');
        const usernameInput = document.getElementById('usernameInput');
        const messageInput = document.getElementById('messageInput');
        const messagesContainer = document.getElementById('messagesContainer');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const userCount = document.getElementById('userCount');
        const connectButton = document.getElementById('connectButton');
        const sendButton = document.getElementById('sendButton');
        const errorMessage = document.getElementById('errorMessage');
        const scrollToBottomBtn = document.getElementById('scrollToBottomBtn');

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            setTimeout(() => {
                errorMessage.style.display = 'none';
            }, 5000);
        }

        function updateConnectionStatus(status, text) {
            statusIndicator.className = 'status-indicator ' + (status === 'connected' ? '' : 'status-disconnected');
            statusText.textContent = text;
        }

        async function joinChat() {
            const usernameValue = usernameInput.value.trim();

            if (!usernameValue) {
                showError('Please enter a username');
                return;
            }

            if (usernameValue.length < 2 || usernameValue.length > 30) {
                showError('Username must be between 2 and 30 characters');
                return;
            }

            username = usernameValue;
            connectButton.disabled = true;
            connectButton.textContent = 'Joining...';

            try {
                // Send join request
                const response = await fetch('/api/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username: username,
                        action: 'join'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    connected = true;
                    loginScreen.style.display = 'none';
                    chatMain.style.display = 'flex';
                    messageInput.focus();

                    // Start real-time connection
                    startRealTimeConnection();

                    updateConnectionStatus('connected', 'Connected (Real-time)');
                } else {
                    showError(data.error || 'Failed to join chat');
                    connectButton.disabled = false;
                    connectButton.textContent = 'Join Chat';
                }
            } catch (error) {
                showError('Connection failed. Please try again.');
                connectButton.disabled = false;
                connectButton.textContent = 'Join Chat';
            }
        }

        function startRealTimeConnection() {
            // Use Server-Sent Events for real-time messaging
            eventSource = new EventSource(`/api/chat-stream?since=${lastMessageId}`);

            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (error) {
                    console.error('Error parsing SSE message:', error);
                }
            };

            eventSource.onerror = function(error) {
                console.error('SSE error:', error);
                updateConnectionStatus('disconnected', 'Connection lost - Reconnecting...');

                // Attempt to reconnect after delay
                setTimeout(() => {
                    if (connected) {
                        startRealTimeConnection();
                    }
                }, 3000);
            };

            eventSource.onopen = function() {
                updateConnectionStatus('connected', 'Connected (Real-time)');
            };
        }

        function handleMessage(data) {
            if (data.type === 'message') {
                addMessage(data.username, data.message, data.username === username, data.timestamp);
                lastMessageId = Math.max(lastMessageId, data.id || 0);
            } else if (data.type === 'system') {
                addSystemMessage(data.message);
                lastMessageId = Math.max(lastMessageId, data.id || 0);
            } else if (data.type === 'user_count') {
                updateUserCount(data.count);
            }
        }

        async function sendMessage() {
            const message = messageInput.value.trim();

            if (!message || !connected) {
                return;
            }

            sendButton.disabled = true;
            const originalText = sendButton.textContent;
            sendButton.textContent = 'Sending...';

            try {
                const response = await fetch('/api/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        username: username,
                        message: message 
                    })
                });

                const data = await response.json();

                if (data.success) {
                    messageInput.value = '';
                    // Message will appear via SSE
                } else {
                    showError('Failed to send message');
                }
            } catch (error) {
                showError('Failed to send message');
            } finally {
                sendButton.disabled = false;
                sendButton.textContent = originalText;
            }
        }

        function addMessage(sender, message, isOwn, timestamp) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isOwn ? 'own' : 'other'}`;

            const time = timestamp ? 
                new Date(timestamp).toLocaleTimeString(undefined, {
                    hour: '2-digit',
                    minute: '2-digit'
                }) :
                new Date().toLocaleTimeString(undefined, {
                    hour: '2-digit',
                    minute: '2-digit'
                });

            messageDiv.innerHTML = `
                ${!isOwn ? `<div class="message-sender">${escapeHtml(sender)}</div>` : ''}
                <div class="message-content">${escapeHtml(message)}</div>
                <div class="message-time">${time}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            // Auto-scroll to bottom if user was already near the bottom
            if (isOwn || isNearBottom()) {
                setTimeout(() => scrollToBottom(true), 10);
            }
        }

        function addSystemMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `<div class="message-content">${escapeHtml(message)}</div>`;

            messagesContainer.appendChild(messageDiv);
            // Always scroll to bottom for system messages
            setTimeout(() => scrollToBottom(true), 10);
        }

        function updateUserCount(count) {
            userCount.textContent = `${count} user${count !== 1 ? 's' : ''} online`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Improved scroll to bottom function
        function scrollToBottom(smooth = true) {
            if (smooth) {
                messagesContainer.scrollTo({
                    top: messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            } else {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }

        // Check if user is near bottom of messages
        function isNearBottom() {
            const threshold = 100; // pixels from bottom
            return messagesContainer.scrollHeight - messagesContainer.scrollTop - messagesContainer.clientHeight < threshold;
        }

        // Event listeners
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        usernameInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                joinChat();
            }
        });

        // Handle scroll to bottom button visibility
        messagesContainer.addEventListener('scroll', function() {
            if (isNearBottom()) {
                scrollToBottomBtn.classList.remove('visible');
            } else {
                scrollToBottomBtn.classList.add('visible');
            }
        });

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            if (connected && username) {
                // Send leave message
                navigator.sendBeacon('/api/send-message', JSON.stringify({
                    username: username,
                    action: 'leave'
                }));
            }

            if (eventSource) {
                eventSource.close();
            }
        });

        // Auto-focus username input
        usernameInput.focus();

        // Show welcome message and demo messages for testing scrolling
        setTimeout(() => {
            if (!connected) {
                addSystemMessage("Welcome! Enter your username to start chatting with real people.");

                // Add some demo messages to test scrolling (only if not connected)
                setTimeout(() => {
                    if (!connected) {
                        addMessage("Alice", "Hey everyone! How's it going?", false);
                        addMessage("Bob", "Great! Just testing this awesome chat app.", false);
                        addMessage("Charlie", "The scrolling works perfectly now!", false);
                        addMessage("Diana", "I love the smooth scroll to bottom feature.", false);
                        addMessage("Eve", "This is a really nice chat interface.", false);
                        addMessage("Frank", "The real-time updates are so smooth!", false);
                        addSystemMessage("Demo messages loaded. Try scrolling up and see the scroll-to-bottom button!");
                    }
                }, 2000);
            }
        }, 1000);
    </script>
</body>
</html>
